{"CHK20250001": {"id": "CHK20250001", "activityId": "ACT20250001", "memberId": "SN20210001", "memberName": "<PERSON><PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-09T12:13:26.759Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-09T12:13:26.759Z"}, "CHK20250002": {"id": "CHK20250002", "activityId": "ACT20250001", "memberId": "SN20210002", "memberName": "<PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-09T12:13:32.615Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-09T12:13:32.615Z"}, "CHK20250003": {"id": "CHK20250003", "activityId": "ACT20250002", "memberId": "SN20210001", "memberName": "<PERSON><PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-09T12:13:40.695Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-09T12:13:40.695Z"}}