<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Course Feedback - <PERSON><PERSON>avi</title>
    <link rel="icon" type="image/png" href="assets/picture/snownavi_logo.png">
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-light);
            color: var(--text-dark);
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--contrast-white);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: var(--contrast-white);
            color: var(--text-dark);
            padding: 2rem;
            text-align: center;
            border-bottom: 3px solid var(--main-red);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--main-red) 0%, var(--accent-blue) 100%);
        }

        .brand-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .brand-logo img {
            height: 40px;
            width: auto;
        }

        .brand-name {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--main-red);
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }

        .header p {
            color: var(--text-gray);
            font-size: 1.1rem;
        }

        .content {
            padding: 2rem;
        }

        /* Email Search Section */
        .search-section {
            background: var(--bg-light);
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            border: 1px solid rgba(229, 53, 18, 0.1);
        }

        .search-section h2 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .search-section p {
            color: var(--text-gray);
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }

        .search-form {
            display: flex;
            gap: 1rem;
            max-width: 400px;
            margin: 0 auto;
        }

        .email-input {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            font-family: inherit;
        }

        .email-input:focus {
            outline: none;
            border-color: var(--main-red);
            box-shadow: 0 0 0 3px rgba(229, 53, 18, 0.1);
        }

        .search-btn {
            padding: 0.75rem 1.5rem;
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .search-btn:hover {
            background: #c42e0f;
        }

        .search-btn:disabled {
            background: var(--text-gray);
            cursor: not-allowed;
        }

        /* Course Selection */
        .course-selection {
            margin-bottom: 2rem;
            display: none;
        }

        .course-selection label {
            display: block;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .course-select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            background: white;
        }

        .course-select:focus {
            outline: none;
            border-color: var(--main-red);
            box-shadow: 0 0 0 3px rgba(229, 53, 18, 0.1);
        }

        /* Feedback Display */
        .feedback-display {
            display: none;
        }

        .student-info {
            background: var(--bg-light);
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border-left: 4px solid var(--main-red);
            border: 1px solid rgba(229, 53, 18, 0.1);
        }

        .student-info h3 {
            color: var(--main-red);
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9rem;
        }

        .info-value {
            color: #333;
            margin-top: 0.25rem;
        }

        /* Skill Assessment Display */
        .skill-assessment {
            margin-bottom: 2rem;
        }

        .skill-section {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .skill-section.completed {
            border-color: #28a745;
            background-color: #f8fff9;
        }

        .section-header {
            background: var(--bg-light);
            padding: 1rem;
            border-bottom: 1px solid #ddd;
        }

        .skill-section.completed .section-header {
            background: #d4edda;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--main-red);
            margin-bottom: 0.5rem;
        }

        .skill-section.completed .section-title {
            color: #28a745;
        }

        .progress-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #666;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #28a745;
            transition: width 0.3s ease;
        }

        .skill-content {
            padding: 1rem;
        }

        .skill-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .skill-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .skill-item.completed {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }

        .skill-item.completed.new {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            animation: pulse 2s infinite;
        }

        .skill-item.historical {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            opacity: 0.8;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .skill-icon {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .skill-name {
            font-size: 0.9rem;
            line-height: 1.3;
        }

        .skill-indicator {
            font-size: 0.75rem;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            margin-left: 0.5rem;
        }

        .skill-indicator.new {
            background-color: #ffc107;
            color: #212529;
            font-weight: bold;
        }

        .skill-indicator.historical {
            background-color: #6c757d;
            color: white;
            font-style: italic;
        }

        .section-feedback {
            background: var(--bg-light);
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            border: 1px solid rgba(229, 53, 18, 0.1);
        }

        .section-feedback h4 {
            color: var(--main-red);
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .section-feedback-text {
            color: var(--text-dark);
            line-height: 1.5;
            font-style: italic;
        }

        /* Overall Feedback */
        .overall-feedback {
            background: var(--contrast-white);
            border: 2px solid var(--main-red);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(229, 53, 18, 0.1);
        }

        .overall-feedback h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .overall-feedback-text {
            color: var(--text-dark);
            line-height: 1.6;
            font-size: 1.05rem;
        }

        /* Historical Progress */
        .historical-progress-section {
            background: var(--contrast-white);
            border: 2px solid #6c757d;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.1);
        }

        .historical-progress-section h3 {
            color: #6c757d;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .historical-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .progress-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .progress-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .progress-card.current {
            border-color: var(--main-red);
            background: #fff5f5;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }

        .progress-header h4 {
            margin: 0;
            color: var(--text-dark);
            font-size: 1rem;
        }

        .progress-card.current .progress-header h4 {
            color: var(--main-red);
        }

        .progress-stats {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
        }

        .skills-count, .sessions-count {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .skills-count {
            font-weight: bold;
            color: #28a745;
        }

        .progress-date {
            font-size: 0.8rem;
            color: #6c757d;
            font-style: italic;
        }

        /* Feedback Meta */
        .feedback-meta {
            background: var(--bg-light);
            padding: 1rem;
            border-radius: 6px;
            border-top: 3px solid var(--main-red);
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .meta-item {
            margin-bottom: 0.5rem;
        }

        .meta-item:last-child {
            margin-bottom: 0;
        }

        /* Loading and Error States */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            margin: 1rem 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #c3e6cb;
            margin: 1rem 0;
        }

        .no-feedback {
            text-align: center;
            padding: 3rem 2rem;
            color: #666;
        }

        .no-feedback h3 {
            color: #999;
            margin-bottom: 1rem;
        }

        /* Footer Section */
        .footer-section {
            background: var(--bg-light);
            padding: 2rem;
            margin-top: 2rem;
            border-top: 3px solid var(--main-red);
        }

        .contact-info {
            text-align: center;
            margin-bottom: 2rem;
        }

        .contact-info h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .contact-details {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
        }

        .contact-icon {
            font-size: 1.2rem;
        }

        .contact-item a {
            color: var(--main-red);
            text-decoration: none;
            font-weight: 500;
        }

        .contact-item a:hover {
            text-decoration: underline;
        }

        .social-media {
            text-align: center;
            margin-bottom: 2rem;
        }

        .social-media h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .social-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            background: var(--contrast-white);
            border-radius: 8px;
            text-decoration: none;
            color: var(--text-dark);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            font-weight: 500;
            position: relative;
            cursor: pointer;
            border: 1px solid rgba(229, 53, 18, 0.1);
        }

        .social-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .social-icon {
            width: 24px;
            height: 24px;
            object-fit: contain;
        }

        .qrcode-popup {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--contrast-white);
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            padding: 1rem;
            margin-bottom: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            z-index: 1000;
            min-width: 200px;
            text-align: center;
            border: 2px solid var(--main-red);
        }

        .social-link:hover .qrcode-popup {
            opacity: 1;
            visibility: visible;
        }

        .qrcode-popup::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 8px solid transparent;
            border-top-color: var(--contrast-white);
        }

        .qrcode-image {
            width: 150px;
            height: 150px;
            object-fit: contain;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }

        .qrcode-text {
            font-size: 0.9rem;
            color: var(--text-gray);
            margin: 0;
        }

        .footer-note {
            text-align: center;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(229, 53, 18, 0.2);
            color: var(--text-gray);
            font-size: 0.9rem;
        }

        .footer-note p {
            margin: 0.25rem 0;
        }

        /* Certificate Section */
        .certificate-section {
            background: var(--contrast-white);
            border: 2px solid var(--main-red);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 2px 8px rgba(229, 53, 18, 0.1);
        }

        .certificate-section h3 {
            color: var(--main-red);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .certificate-section p {
            color: var(--text-gray);
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .certificate-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .certificate-btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 150px;
            justify-content: center;
        }

        .certificate-btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }

        .certificate-btn:active {
            transform: translateY(0);
        }

        .certificate-btn:disabled {
            background: var(--text-gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Mobile Optimization */
        @media (max-width: 768px) {
            body {
                padding: 1rem 0.5rem;
            }

            .header {
                padding: 1.5rem 1rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .content {
                padding: 1rem;
            }

            .search-section {
                padding: 1.5rem 1rem;
            }

            .search-form {
                flex-direction: column;
                max-width: none;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .skill-items {
                grid-template-columns: 1fr;
            }

            .progress-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .progress-bar {
                width: 100%;
            }

            .contact-details {
                flex-direction: column;
                gap: 1rem;
            }

            .social-links {
                flex-direction: column;
                align-items: center;
            }

            .social-link {
                width: 100%;
                max-width: 280px;
                justify-content: center;
            }

            .certificate-section {
                padding: 1rem;
            }

            .certificate-btn {
                width: 100%;
                max-width: 280px;
            }

            /* Timeline mobile styles */
            .timeline-container {
                padding-left: 1.5rem;
            }

            .timeline-container::before {
                left: 0.75rem;
            }

            .timeline-marker {
                left: -1.25rem;
                width: 1.5rem;
                height: 1.5rem;
            }

            .timeline-number {
                font-size: 0.7rem;
            }

            .timeline-header {
                flex-direction: column;
                gap: 0.25rem;
            }

            .timeline-stats {
                flex-direction: column;
                gap: 0.5rem;
            }

            .skills-list {
                margin-left: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.3rem;
            }

            .header p {
                font-size: 1rem;
            }

            .search-section {
                padding: 1rem;
            }

            .skill-content {
                padding: 0.75rem;
            }

            .section-header {
                padding: 0.75rem;
            }

            .footer-section {
                padding: 1.5rem 1rem;
            }

            .brand-logo {
                flex-direction: column;
                gap: 0.5rem;
            }

            .brand-name {
                font-size: 1.5rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .contact-details {
                gap: 0.75rem;
            }

            .social-link {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            /* Timeline small screen styles */
            .learning-timeline {
                padding: 0.75rem;
            }

            .timeline-content {
                padding: 0.75rem;
            }

            .timeline-skills strong {
                font-size: 0.85rem;
            }

            .skills-list li {
                font-size: 0.8rem;
            }
        }

        /* Student Rating Section */
        .student-rating-section {
            margin-top: 2rem;
            padding: 1.5rem;
            background: var(--bg-light);
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
        }

        .student-rating-section h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }

        .rating-container {
            margin: 1rem 0;
        }

        .rating-label {
            display: block;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .star-rating {
            display: flex;
            gap: 0.25rem;
            margin-bottom: 1rem;
            align-items: center;
        }

        .star {
            font-size: 2rem;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s ease;
            user-select: none;
        }

        .star:hover,
        .star.active {
            color: #ffc107;
        }

        .star.half {
            background: linear-gradient(90deg, #ffc107 50%, #ddd 50%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .rating-value {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--main-red);
            margin-left: 0.5rem;
        }

        .comment-container {
            margin: 1rem 0;
        }

        .comment-textarea {
            width: 100%;
            min-height: 100px;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-family: inherit;
            font-size: 1rem;
            resize: vertical;
            transition: border-color 0.2s ease;
            box-sizing: border-box;
        }

        .comment-textarea:focus {
            outline: none;
            border-color: var(--main-red);
        }

        .submit-rating-btn {
            background: var(--main-red);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .submit-rating-btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
        }

        .submit-rating-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .rating-success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            border-left: 4px solid #28a745;
        }

        .rating-error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            border-left: 4px solid #dc3545;
        }

        /* Learning Timeline Styles */
        .learning-timeline {
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid var(--accent-blue);
        }

        .learning-timeline h4 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .timeline-container {
            position: relative;
            padding-left: 2rem;
        }

        .timeline-container::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--main-red), var(--accent-blue));
        }

        .timeline-item {
            position: relative;
            margin-bottom: 1.5rem;
            padding-left: 1.5rem;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
        }

        .timeline-marker {
            position: absolute;
            left: -1.5rem;
            top: 0.5rem;
            width: 2rem;
            height: 2rem;
            background: var(--contrast-white);
            border: 3px solid var(--main-red);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
        }

        .timeline-item.current .timeline-marker {
            background: var(--main-red);
            border-color: var(--main-red);
            animation: pulse-marker 2s infinite;
        }

        @keyframes pulse-marker {
            0% { box-shadow: 0 0 0 0 rgba(229, 53, 18, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(229, 53, 18, 0); }
            100% { box-shadow: 0 0 0 0 rgba(229, 53, 18, 0); }
        }

        .timeline-number {
            font-size: 0.8rem;
            font-weight: bold;
            color: var(--main-red);
        }

        .timeline-item.current .timeline-number {
            color: var(--contrast-white);
        }

        .timeline-content {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .timeline-item.current .timeline-content {
            border-color: var(--main-red);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.2);
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }

        .timeline-header h5 {
            margin: 0;
            color: var(--text-dark);
            font-size: 1rem;
        }

        .timeline-item.current .timeline-header h5 {
            color: var(--main-red);
        }

        .timeline-date {
            font-size: 0.85rem;
            color: var(--text-gray);
            font-style: italic;
        }

        .timeline-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.75rem;
            flex-wrap: wrap;
        }

        .new-skills-count, .total-skills-count {
            font-size: 0.85rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }

        .new-skills-count {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .total-skills-count {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .timeline-skills {
            margin-top: 0.75rem;
            padding-top: 0.75rem;
            border-top: 1px solid #e0e0e0;
        }

        .timeline-skills strong {
            color: var(--main-red);
            font-size: 0.9rem;
        }

        .skills-list {
            margin: 0.5rem 0 0 1rem;
            padding: 0;
        }

        .skills-list li {
            margin-bottom: 0.25rem;
            font-size: 0.85rem;
            color: var(--text-dark);
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="brand-logo">
                <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
                <span class="brand-name">SnowNavi</span>
            </div>
            <h1>🎿 My Course Feedback</h1>
            <p>View your snowboarding progress and instructor feedback</p>
        </div>

        <div class="content">
            <!-- Email Search Section -->
            <div class="search-section">
                <h2>Find Your Feedback</h2>
                <p>Enter your email address to view your course feedback and progress</p>
                <div class="search-form">
                    <input 
                        type="email" 
                        id="email-input" 
                        class="email-input" 
                        placeholder="Enter your email address"
                        required
                    >
                    <button id="search-btn" class="search-btn" onclick="searchFeedback()">
                        Search
                    </button>
                </div>
                <div id="search-message"></div>
            </div>

            <!-- Course Selection -->
            <div id="course-selection" class="course-selection">
                <label for="course-select">Select Course:</label>
                <div style="display: flex; gap: 0.5rem; align-items: center;">
                    <select id="course-select" class="course-select" onchange="displaySelectedFeedback()" style="flex: 1;">
                        <option value="">Choose a course...</option>
                    </select>
                </div>
            </div>

            <!-- Feedback Display -->
            <div id="feedback-display" class="feedback-display">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>

        <!-- Contact and Social Media Footer -->
        <div class="footer-section">
            <div class="contact-info">
                <h3>Contact SnowNavi</h3>
                <div class="contact-details">
                    <div class="contact-item">
                        <span class="contact-icon">📧</span>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div class="contact-item">
                        <span class="contact-icon">🌐</span>
                        <a href="https://snownavi.ski" target="_blank">snownavi.ski</a>
                    </div>
                </div>
            </div>

            <div class="social-media">
                <h3>Follow Us</h3>
                <div class="social-links">
                    <div class="social-link" onclick="showQRCode('wechat')">
                        <img src="assets/picture/wechat_logo.png" alt="WeChat" class="social-icon">
                        <span>SnowNavi指雪针</span>
                        <div class="qrcode-popup" id="wechat-qr">
                            <img src="assets/picture/wechat_qrcode.jpg" alt="WeChat QR Code" class="qrcode-image">
                            <p class="qrcode-text">Scan to follow WeChat</p>
                        </div>
                    </div>
                    <div class="social-link" onclick="showQRCode('xiaohongshu')">
                        <img src="assets/picture/xiaohongshu_logo.png" alt="Little Red Book" class="social-icon">
                        <span>SnowNavi指雪针</span>
                        <div class="qrcode-popup" id="xiaohongshu-qr">
                            <img src="assets/picture/xiaohongshu_qrcode.jpg" alt="Xiaohongshu QR Code" class="qrcode-image">
                            <p class="qrcode-text">Scan to follow Xiaohongshu</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-note">
                <p>&copy; 2025 SnowNavi Snow Club. All rights reserved.</p>
                <p>Professional snowboarding instruction since 2021</p>
            </div>
        </div>
    </div>

    <script src="certificate-generator.js"></script>
    <script>
        let studentFeedbacks = [];
        let activities = {};
        let feedbackTemplates = {};
        let currentEmail = '';
        let certificateGenerator = null;

        // Load activities and feedback templates data
        async function loadActivities() {
            try {
                const response = await fetch('data/activities.json');
                if (response.ok) {
                    activities = await response.json();
                }
            } catch (error) {
                console.error('Error loading activities:', error);
            }
        }

        // Load feedback templates data with cache busting
        async function loadFeedbackTemplates(forceReload = false) {
            try {
                // Add timestamp to prevent caching when force reloading
                const cacheBuster = forceReload ? `?t=${Date.now()}` : '';
                const response = await fetch(`data/feedback_templates.json${cacheBuster}`);
                if (response.ok) {
                    feedbackTemplates = await response.json();
                    console.log('Loaded feedback templates for certificates:', Object.keys(feedbackTemplates));
                    return true;
                }
            } catch (error) {
                console.warn('Error loading feedback templates:', error);
                feedbackTemplates = {};
                return false;
            }
        }

        // Reload feedback templates to get latest changes
        async function reloadFeedbackTemplates() {
            console.log('Reloading feedback templates to get latest changes...');
            const success = await loadFeedbackTemplates(true);
            if (success) {
                console.log('✅ Feedback templates reloaded successfully');
            } else {
                console.warn('❌ Failed to reload feedback templates');
            }
            return success;
        }

        // Refresh feedback display with latest templates
        async function refreshFeedbackDisplay() {
            const btn = event.target;
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '🔄 Refreshing...';

            try {
                // Reload templates
                const success = await reloadFeedbackTemplates();

                if (success) {
                    // Refresh the current feedback display
                    await displaySelectedFeedback();

                    // Show success message briefly
                    btn.textContent = '✅ Refreshed!';
                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.disabled = false;
                    }, 1500);
                } else {
                    throw new Error('Failed to reload templates');
                }
            } catch (error) {
                console.error('Error refreshing feedback display:', error);
                btn.textContent = '❌ Error';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }

        async function searchFeedback() {
            const email = document.getElementById('email-input').value.trim().toLowerCase();
            const searchBtn = document.getElementById('search-btn');
            const messageDiv = document.getElementById('search-message');
            
            if (!email) {
                showMessage('Please enter your email address.', 'error');
                return;
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('Please enter a valid email address.', 'error');
                return;
            }

            searchBtn.disabled = true;
            searchBtn.textContent = 'Searching...';
            messageDiv.innerHTML = '<div class="loading">🔍 Searching for your feedback...</div>';

            try {
                // Load feedbacks data
                const feedbackResponse = await fetch('data/feedbacks.json');
                if (!feedbackResponse.ok) {
                    throw new Error('Unable to load feedback data');
                }
                
                const allFeedbacks = await feedbackResponse.json();
                
                // Load members data to get member info by email
                const membersResponse = await fetch('data/members.json');
                if (!membersResponse.ok) {
                    throw new Error('Unable to load member data');
                }
                
                const members = await membersResponse.json();
                
                // Find member by email
                let memberInfo = null;
                let memberId = null;
                
                for (const [id, member] of Object.entries(members)) {
                    if (member.email && member.email.toLowerCase() === email) {
                        memberInfo = member;
                        memberId = id;
                        break;
                    }
                }
                
                if (!memberInfo) {
                    showMessage('No member found with this email address. Please check your email or contact support.', 'error');
                    return;
                }
                
                // Filter feedbacks for this member
                studentFeedbacks = [];
                for (const feedback of Object.values(allFeedbacks)) {
                    if (feedback.memberId === memberId) {
                        studentFeedbacks.push(feedback);
                    }
                }
                
                if (studentFeedbacks.length === 0) {
                    showMessage('No feedback found for this email address. Complete a course to receive feedback!', 'error');
                    return;
                }
                
                // Sort feedbacks by creation date (newest first)
                studentFeedbacks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                
                currentEmail = email;
                showMessage(`Found ${studentFeedbacks.length} feedback record(s) for ${memberInfo.name}!`, 'success');
                
                // Populate course selection
                populateCourseSelection();
                
                // Reload templates before displaying feedback
                await reloadFeedbackTemplates();

                // Show course selection and display latest feedback
                document.getElementById('course-selection').style.display = 'block';
                displaySelectedFeedback();
                
            } catch (error) {
                console.error('Error searching feedback:', error);
                showMessage('Error searching for feedback. Please try again later.', 'error');
            } finally {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';
            }
        }

        function populateCourseSelection() {
            const courseSelect = document.getElementById('course-select');
            courseSelect.innerHTML = '<option value="">Choose a course...</option>';
            
            studentFeedbacks.forEach((feedback, index) => {
                const activity = activities[feedback.activityId];
                const activityName = activity ? activity.name.en : 'Unknown Activity';
                const date = new Date(feedback.createdAt).toLocaleDateString();
                const isLatest = index === 0 ? ' (Latest)' : '';
                
                const option = document.createElement('option');
                option.value = index;
                option.textContent = `${activityName} - ${date}${isLatest}`;
                courseSelect.appendChild(option);
            });
            
            // Select the latest feedback by default
            courseSelect.value = '0';
        }

        async function displaySelectedFeedback() {
            const courseSelect = document.getElementById('course-select');
            const selectedIndex = courseSelect.value;

            if (selectedIndex === '') {
                document.getElementById('feedback-display').style.display = 'none';
                return;
            }

            const feedback = studentFeedbacks[selectedIndex];
            if (!feedback) return;

            // Reload templates to ensure we have the latest version for display
            await reloadFeedbackTemplates();

            const activity = activities[feedback.activityId];
            const activityName = activity ? activity.name.en : 'Unknown Activity';
            const activityDate = activity ? new Date(activity.date).toLocaleDateString() : 'Unknown Date';

            const feedbackDisplay = document.getElementById('feedback-display');
            feedbackDisplay.innerHTML = await generateFeedbackHTML(feedback, activityName, activityDate);
            feedbackDisplay.style.display = 'block';

            // Initialize star ratings after content is loaded
            setTimeout(() => {
                initializeAllStarRatings();
            }, 100);
        }

        async function generateFeedbackHTML(feedback, activityName, activityDate) {
            let html = `
                <div class="student-info">
                    <h3>Course Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Student Name</span>
                            <span class="info-value">${feedback.memberName}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Course</span>
                            <span class="info-value">${activityName}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Course Date</span>
                            <span class="info-value">${activityDate}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Feedback Date</span>
                            <span class="info-value">${new Date(feedback.createdAt).toLocaleDateString()}</span>
                        </div>
                    </div>
                </div>
            `;
            
            // Historical Progress Section
            html += await generateHistoricalProgressHTML(feedback.memberId, feedback.activityId);

            // Skill Assessment
            if (feedback.skillAssessment && feedback.skillAssessment.completedSkills) {
                html += await generateSkillAssessmentHTML(feedback.skillAssessment, feedback.activityId, feedback.memberId);
            }
            
            // Overall Feedback
            const overallFeedback = feedback.overallFeedback || feedback.feedback || '';
            if (overallFeedback) {
                html += `
                    <div class="overall-feedback">
                        <h3>📝 Overall Course Feedback</h3>
                        <div class="overall-feedback-text">${overallFeedback}</div>
                    </div>
                `;
            }
            
            // Certificate Download Section
            html += `
                <div class="certificate-section">
                    <h3>📜 Course Completion Certificate</h3>
                    <p>Download your personalized certificate showing your snowboarding progress and achievements.</p>
                    <div class="certificate-buttons">
                        <button class="certificate-btn" onclick="generateCertificate('${feedback.memberId}', '${feedback.memberName}', '${activityName}', '${activityDate}', 'en')">
                            📥 Download English
                        </button>
                        <button class="certificate-btn" onclick="generateCertificate('${feedback.memberId}', '${feedback.memberName}', '${activityName}', '${activityDate}', 'zh')">
                            📥 Download 中文
                        </button>
                    </div>
                </div>
            `;

            // Student Rating Section
            html += generateStudentRatingHTML(feedback);

            // Feedback Meta Information
            html += `
                <div class="feedback-meta">
                    <div class="meta-item"><strong>Instructor:</strong> ${feedback.createdBy || 'Unknown'}</div>
                    <div class="meta-item"><strong>Created:</strong> ${new Date(feedback.createdAt).toLocaleString()}</div>
                    ${feedback.updatedBy && feedback.updatedAt !== feedback.createdAt ?
                        `<div class="meta-item"><strong>Last Updated:</strong> ${new Date(feedback.updatedAt).toLocaleString()} by ${feedback.updatedBy}</div>` : ''
                    }
                </div>
            `;

            return html;
        }

        async function generateSkillAssessmentHTML(skillAssessment, activityId, memberId) {
            // Reload templates to ensure we have the latest version
            await reloadFeedbackTemplates();

            // Get template data for the activity
            let templateData = null;
            let currentTemplateId = null;
            if (activityId && activities[activityId]) {
                const activity = activities[activityId];
                currentTemplateId = activity.feedbackTemplateId;

                if (currentTemplateId && feedbackTemplates[currentTemplateId]) {
                    templateData = feedbackTemplates[currentTemplateId];
                    console.log(`Using template: ${templateData.name?.en || currentTemplateId} for skill display`);
                }
            }

            // Load historical skills for this member and template
            let historicalSkills = [];
            if (memberId && currentTemplateId) {
                try {
                    const response = await fetch(`/api/member-skills-progress/${memberId}`);
                    if (response.ok) {
                        const data = await response.json();
                        const progress = data.skillsProgress[currentTemplateId];
                        if (progress && progress.completedSkills) {
                            historicalSkills = progress.completedSkills;
                        }
                    }
                } catch (error) {
                    console.error('Error loading historical skills:', error);
                }
            }

            let skillSections = {};

            if (templateData && templateData.sections) {
                // Build sections from template data
                const sortedSections = Object.entries(templateData.sections).sort((a, b) => {
                    return (a[1].order || 0) - (b[1].order || 0);
                });

                sortedSections.forEach(([sectionId, section]) => {
                    const sectionTitle = section.title.zh || section.title.en || sectionId;
                    const sectionTitleEn = section.title.en || sectionId;
                    const displayTitle = sectionTitle.includes('(') ? sectionTitle : `${sectionTitle} (${sectionTitleEn})`;

                    const skills = {};
                    if (section.skills) {
                        // Sort skills by order
                        const sortedSkills = Object.entries(section.skills).sort((a, b) => {
                            return (a[1].order || 0) - (b[1].order || 0);
                        });

                        sortedSkills.forEach(([skillId, skill]) => {
                            const skillName = skill.name.zh || skill.name.en || skillId;
                            const skillNameEn = skill.name.en || skillId;
                            const displayName = skillName.includes('(') ? skillName : `${skillName} (${skillNameEn})`;
                            skills[skillId] = displayName;
                        });
                    }

                    skillSections[sectionId] = {
                        title: displayTitle,
                        skills: skills
                    };
                });

                console.log(`Generated skill sections from template: ${Object.keys(skillSections).join(', ')}`);
            } else {
                // Fall back to default hardcoded structure
                console.log('Using default skill sections (no template found)');
                skillSections = {
                    basic: {
                        title: 'Basic 基础知识',
                        skills: {
                            'equipment-intro': '滑雪装备介绍 (Equipment Introduction)',
                            'single-foot-familiarity': '单脚熟悉雪板 (Single Foot Board Familiarity)'
                        }
                    },
                    sliding: {
                        title: 'Sliding 滑行',
                        skills: {
                            'single-foot-sliding': '单脚滑板式滑动 (Single Foot Skateboard Sliding)',
                            'single-foot-climbing': '单脚爬坡 (Single Foot Climbing)',
                            'single-foot-straight': '单脚直滑降 (Single Foot Straight Descent)',
                            'single-foot-heel-brake': '单脚脚后跟减速 (Single Foot Heel Braking)',
                            'single-foot-j-turn': '单脚J弯 (Single Foot J-Turn)'
                        }
                    },
                    control: {
                        title: 'Control 控制',
                        skills: {
                            'static-gas-pedal': '静态踩油门练习 (Static Gas Pedal Practice)',
                            'single-heel-side-push': '单脚后刃推坡 (Single Foot Heel Side Push)',
                            'single-toe-side-push': '单脚前刃推坡 (Single Foot Toe Side Push)',
                            'both-heel-side-push': '双脚后刃推坡 (Both Feet Heel Side Push)',
                            'both-toe-side-push': '双脚前刃推坡 (Both Feet Toe Side Push)',
                            'both-heel-falling-leaf': '双脚后刃落叶飘 (Both Feet Heel Side Falling Leaf)',
                            'both-toe-falling-leaf': '双脚前刃落叶飘 (Both Feet Toe Side Falling Leaf)',
                            'both-heel-power-falling-leaf': '双脚后刃强力落叶飘 (Both Feet Heel Side Power Falling Leaf)',
                            'both-toe-power-falling-leaf': '双脚前刃强力落叶飘 (Both Feet Toe Side Power Falling Leaf)'
                        }
                    },
                    turning: {
                        title: 'Turning 转弯',
                        skills: {
                            'static-rotation': '静态旋转练习 (Static Rotation Practice)',
                            'step-turns': '阶梯转弯 (Step Turns)',
                            'j-turns': 'J弯 (J-Turns)',
                            'walking-edge-change': '走步模拟换刃 (Walking Edge Change Simulation)',
                            'beginner-turns': '新手转弯 (Beginner Turns)'
                        }
                    },
                    flow: {
                        title: 'Flow 流畅性',
                        skills: {
                            'edge-change-traverse': '换刃后增加横穿雪道 (Edge Change with Slope Traverse)',
                            'traverse-body-movement': '横穿雪道加入身体起伏 (Traverse with Body Movement)',
                            'continuous-edge-change': '连续换刃 (Continuous Edge Changes)',
                            'scrub-360': '搓雪360 (Scrub 360)'
                        }
                    }
                };
            }
            
            let html = '<div class="skill-assessment"><h3>🎯 Skill Assessment</h3>';
            
            Object.entries(skillSections).forEach(([sectionId, section]) => {
                const sectionSkills = Object.keys(section.skills);

                // Count skills completed in current session
                const currentlyCompletedSkills = sectionSkills.filter(skill =>
                    skillAssessment.completedSkills.includes(skill)
                );

                // Count skills completed historically (but not in current session)
                const historicallyCompletedSkills = sectionSkills.filter(skill =>
                    historicalSkills.includes(skill) && !skillAssessment.completedSkills.includes(skill)
                );

                // Total completed skills (current + historical)
                const allCompletedSkills = sectionSkills.filter(skill =>
                    skillAssessment.completedSkills.includes(skill) || historicalSkills.includes(skill)
                );

                const totalSkills = sectionSkills.length;
                const completedCount = allCompletedSkills.length;
                const currentCount = currentlyCompletedSkills.length;
                const historicalCount = historicallyCompletedSkills.length;
                const percentage = totalSkills > 0 ? (completedCount / totalSkills) * 100 : 0;
                const isCompleted = completedCount === totalSkills && totalSkills > 0;
                
                // Create detailed progress text
                let progressText = `${completedCount}/${totalSkills} completed`;
                if (currentCount > 0 && historicalCount > 0) {
                    progressText += ` (${currentCount} new, ${historicalCount} previous)`;
                } else if (currentCount > 0) {
                    progressText += ` (${currentCount} new)`;
                } else if (historicalCount > 0) {
                    progressText += ` (${historicalCount} previous)`;
                }

                html += `
                    <div class="skill-section ${isCompleted ? 'completed' : ''}">
                        <div class="section-header">
                            <div class="section-title">${section.title}</div>
                            <div class="progress-info">
                                <span class="progress-text">${progressText}</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${percentage}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="skill-content">
                            <div class="skill-items">
                `;
                
                Object.entries(section.skills).forEach(([skillId, skillName]) => {
                    const isCurrentlyCompleted = skillAssessment.completedSkills.includes(skillId);
                    const isHistoricallyCompleted = historicalSkills.includes(skillId);
                    const isNewlyCompleted = isCurrentlyCompleted && !isHistoricallyCompleted;

                    let skillClass = '';
                    let skillIcon = '⭕';
                    let skillIndicator = '';

                    if (isCurrentlyCompleted) {
                        if (isNewlyCompleted) {
                            skillClass = 'completed new';
                            skillIcon = '🆕';
                            skillIndicator = ' <span class="skill-indicator new">New!</span>';
                        } else {
                            skillClass = 'completed';
                            skillIcon = '✅';
                        }
                    } else if (isHistoricallyCompleted) {
                        skillClass = 'historical';
                        skillIcon = '📚';
                        skillIndicator = ' <span class="skill-indicator historical">Previously mastered</span>';
                    }

                    html += `
                        <div class="skill-item ${skillClass}">
                            <span class="skill-icon">${skillIcon}</span>
                            <span class="skill-name">${skillName}${skillIndicator}</span>
                        </div>
                    `;
                });
                
                html += '</div>';
                
                // Section feedback
                if (skillAssessment.sectionFeedbacks && skillAssessment.sectionFeedbacks[sectionId]) {
                    html += `
                        <div class="section-feedback">
                            <h4>Instructor Notes:</h4>
                            <div class="section-feedback-text">${skillAssessment.sectionFeedbacks[sectionId]}</div>
                        </div>
                    `;
                }
                
                html += '</div></div>';
            });
            
            html += '</div>';
            return html;
        }

        function generateStudentRatingHTML(feedback) {
            // Check if student has already submitted a rating for this feedback
            const hasExistingRating = feedback.studentRating && feedback.studentRating.rating;

            let html = `
                <div class="student-rating-section">
                    <h3>⭐ Rate Your Instructor</h3>
                    <p>Help us improve by rating your instructor and leaving feedback about your learning experience.</p>
            `;

            if (hasExistingRating) {
                // Show existing rating
                const rating = feedback.studentRating.rating;
                const comment = feedback.studentRating.comment || '';
                const submittedDate = new Date(feedback.studentRating.submittedAt).toLocaleDateString();

                html += `
                    <div class="rating-success">
                        <h4>✅ Thank you for your feedback!</h4>
                        <p><strong>Your Rating:</strong> ${rating}/5 stars </p>
                        ${comment ? `<p><strong>Your Comment:</strong> "${comment}"</p>` : ''}
                        <p><strong>Submitted:</strong> ${submittedDate}</p>
                    </div>
                `;
            } else {
                // Show rating form
                html += `
                    <div class="rating-container">
                        <label class="rating-label">Rate your instructor (1-5 stars):</label>
                        <div class="star-rating" id="star-rating-${feedback.id}">
                            <span class="star" data-star="1">☆</span>
                            <span class="star" data-star="2">☆</span>
                            <span class="star" data-star="3">☆</span>
                            <span class="star" data-star="4">☆</span>
                            <span class="star" data-star="5">☆</span>
                        </div>
                        <span class="rating-value" id="rating-value-${feedback.id}">0/5</span>
                    </div>

                    <div class="comment-container">
                        <label class="rating-label" for="rating-comment-${feedback.id}">Additional Comments (Optional):</label>
                        <textarea
                            id="rating-comment-${feedback.id}"
                            class="comment-textarea"
                            placeholder="Share your thoughts about the instruction, what you learned, or suggestions for improvement..."
                            maxlength="500"
                        ></textarea>
                    </div>

                    <button
                        class="submit-rating-btn"
                        onclick="submitStudentRating('${feedback.id}')"
                        id="submit-rating-${feedback.id}"
                    >
                        Submit Rating
                    </button>

                    <div id="rating-message-${feedback.id}"></div>
                `;
            }

            html += '</div>';
            return html;
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('search-message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Allow Enter key to trigger search
        document.getElementById('email-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchFeedback();
            }
        });

        // QR Code display function
        function showQRCode(platform) {
            // This function can be used for mobile tap events if needed
            console.log(`Showing QR code for ${platform}`);
        }

        // Certificate generation function
        async function generateCertificate(memberId, memberName, activityName, activityDate, language = 'en') {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '🔄 Generating...';

            try {
                // Initialize certificate generator if not already done
                if (!certificateGenerator) {
                    certificateGenerator = new CertificateGenerator();
                }

                // Get current feedback data for skill assessment
                const selectedIndex = document.getElementById('course-select').value;
                const feedback = studentFeedbacks[selectedIndex];

                // Reload templates to ensure we have the latest version
                await reloadFeedbackTemplates();

                // Get template data for the activity
                let templateData = null;
                if (feedback && feedback.activityId && activities[feedback.activityId]) {
                    const activity = activities[feedback.activityId];
                    const templateId = activity.feedbackTemplateId;

                    if (templateId && feedbackTemplates[templateId]) {
                        templateData = feedbackTemplates[templateId];
                        console.log(`Using template: ${templateData.name?.en || templateId} for certificate`);
                    } else if (templateId) {
                        console.warn(`Template ${templateId} not found in loaded templates, trying to reload...`);
                        // Try reloading templates one more time
                        await reloadFeedbackTemplates();
                        if (feedbackTemplates[templateId]) {
                            templateData = feedbackTemplates[templateId];
                            console.log(`Found template after reload: ${templateData.name?.en || templateId}`);
                        } else {
                            console.warn(`Template ${templateId} still not found after reload`);
                        }
                    }
                }

                // Prepare certificate data
                const certificateData = {
                    memberId,
                    memberName,
                    activityName,
                    activityDate,
                    feedback
                };

                // Generate certificate using advanced generator with language and template support
                const canvas = await certificateGenerator.generateAdvancedCertificate(certificateData, language, templateData);

                // Convert to blob and download
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    const languageSuffix = language === 'zh' ? '_CN' : '_EN';
                    a.download = `SnowNavi_Certificate_${memberName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}${languageSuffix}.jpg`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/jpeg', 0.95);

            } catch (error) {
                console.error('Error generating certificate:', error);
                alert('Failed to generate certificate. Please try again.');
            } finally {
                btn.disabled = false;
                const originalText = language === 'zh' ? '📥 Download 中文' : '📥 Download English';
                btn.textContent = originalText;
            }
        }

        let feedbackRating = 0;

        // Star rating functionality with half-star support
        function initializeStarRating(feedbackId) {
            const starContainer = document.getElementById(`star-rating-${feedbackId}`);
            const ratingValue = document.getElementById(`rating-value-${feedbackId}`);
            const stars = starContainer.querySelectorAll('.star');
            let currentRating = 0;

            stars.forEach((star, index) => {
                // Add click event for full and half stars
                star.addEventListener('click', function(event) {
                    const rect = this.getBoundingClientRect();
                    const clickX = event.clientX - rect.left;
                    const starWidth = rect.width;
                    const starNumber = parseInt(this.dataset.star);

                    // Determine if click is on left half (half star) or right half (full star)
                    const isHalfStar = clickX < starWidth / 2;
                    currentRating = isHalfStar ? starNumber - 0.5 : starNumber;
                    feedbackRating = currentRating;

                    updateStarDisplay(stars, currentRating);
                    ratingValue.textContent = `${currentRating}/5`;
                });

                // Add mouseover event for preview
                star.addEventListener('mouseover', function(event) {
                    const rect = this.getBoundingClientRect();
                    const mouseX = event.clientX - rect.left;
                    const starWidth = rect.width;
                    const starNumber = parseInt(this.dataset.star);

                    const isHalfStar = mouseX < starWidth / 2;
                    const previewRating = isHalfStar ? starNumber - 0.5 : starNumber;

                    updateStarDisplay(stars, previewRating);
                    ratingValue.textContent = `${previewRating}/5`;
                });
            });

            starContainer.addEventListener('mouseleave', function() {
                updateStarDisplay(stars, currentRating);
                ratingValue.textContent = currentRating > 0 ? `${currentRating}/5` : '0/5';
            });

            return () => currentRating;
        }

        function updateStarDisplay(stars, rating) {
            stars.forEach((star, index) => {
                const starNumber = parseInt(star.dataset.star);
                star.classList.remove('active', 'half');

                if (starNumber <= rating) {
                    // Full star
                    star.classList.add('active');
                    star.textContent = '★';
                } else if (starNumber - 0.5 <= rating) {
                    // Half star
                    star.classList.add('half');
                    star.textContent = '★';
                } else {
                    // Empty star
                    star.textContent = '☆';
                }
            });
        }

        // Submit student rating
        async function submitStudentRating(feedbackId) {
            const starContainer = document.getElementById(`star-rating-${feedbackId}`);
            const commentTextarea = document.getElementById(`rating-comment-${feedbackId}`);
            const submitBtn = document.getElementById(`submit-rating-${feedbackId}`);
            const messageDiv = document.getElementById(`rating-message-${feedbackId}`);

            // Get current rating
            const activeStars = starContainer.querySelectorAll('.star.active, .star.half');
            let rating = feedbackRating;
            // if (activeStars.length > 0) {
            //     const lastActiveStar = activeStars[activeStars.length - 1];
            //     rating = parseFloat(lastActiveStar.dataset.rating);
            // }

            if (rating === 0) {
                messageDiv.innerHTML = '<div class="rating-error">Please select a rating before submitting.</div>';
                return;
            }

            const comment = commentTextarea.value.trim();

            submitBtn.disabled = true;
            submitBtn.textContent = 'Submitting...';
            messageDiv.innerHTML = '';

            try {
                // Find the feedback object
                const feedback = studentFeedbacks.find(f => f.id === feedbackId);
                if (!feedback) {
                    throw new Error('Feedback not found');
                }

                // Prepare rating data
                const ratingData = {
                    feedbackId: feedbackId,
                    studentEmail: currentEmail,
                    studentName: feedback.memberName,
                    memberId: feedback.memberId,
                    activityId: feedback.activityId,
                    instructorName: feedback.createdBy,
                    rating: rating,
                    comment: comment,
                    submittedAt: new Date().toISOString()
                };

                // Submit to server
                const response = await fetch('/api/submit-student-rating', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(ratingData)
                });

                if (!response.ok) {
                    throw new Error('Failed to submit rating');
                }

                // Update the feedback object with the rating
                feedback.studentRating = ratingData;

                // Show success message and refresh display
                messageDiv.innerHTML = '<div class="rating-success">✅ Thank you for your feedback! Your rating has been submitted successfully.</div>';

                // Refresh the feedback display to show the submitted rating
                setTimeout(() => {
                    displaySelectedFeedback();
                }, 2000);

            } catch (error) {
                console.error('Error submitting rating:', error);
                messageDiv.innerHTML = '<div class="rating-error">❌ Failed to submit rating. Please try again later.</div>';
                submitBtn.disabled = false;
                submitBtn.textContent = 'Submit Rating';
            }
        }

        // Initialize star ratings after feedback is displayed
        function initializeAllStarRatings() {
            // Find all star rating containers and initialize them
            const starContainers = document.querySelectorAll('[id^="star-rating-"]');
            starContainers.forEach(container => {
                const feedbackId = container.id.replace('star-rating-', '');
                initializeStarRating(feedbackId);
            });
        }

        // Historical progress functions
        async function generateHistoricalProgressHTML(memberId, activityId) {
            try {
                // Load historical progress for this member
                const response = await fetch(`/api/member-skills-progress/${memberId}`);
                if (!response.ok) {
                    return ''; // No historical data available
                }

                const data = await response.json();
                const skillsProgress = data.skillsProgress;

                if (!skillsProgress || Object.keys(skillsProgress).length === 0) {
                    return ''; // No historical data
                }

                // Get current activity template
                let currentTemplateId = null;
                if (activityId && activities[activityId]) {
                    currentTemplateId = activities[activityId].feedbackTemplateId;
                }

                // Load all feedbacks for this member to create timeline
                const feedbackResponse = await fetch('data/feedbacks.json');
                const allFeedbacks = feedbackResponse.ok ? await feedbackResponse.json() : {};

                // Filter feedbacks for this member and sort by date
                const memberFeedbacks = Object.values(allFeedbacks)
                    .filter(feedback => feedback.memberId === memberId)
                    .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

                let html = `
                    <div class="historical-progress-section">
                        <h3>📈 Your Learning Journey</h3>
                `;

                // Generate timeline if we have multiple feedbacks
                if (memberFeedbacks.length > 1) {
                    html += await generateLearningTimeline(memberFeedbacks, currentTemplateId);
                }

                // Summary cards
                html += `<div class="historical-summary">`;

                // Display progress for each template
                Object.entries(skillsProgress).forEach(([templateId, progress]) => {
                    const template = feedbackTemplates[templateId];
                    const templateName = template ? template.name.en : templateId;
                    const isCurrent = templateId === currentTemplateId;

                    // Calculate total skills in template
                    let totalSkillsInTemplate = 0;
                    if (template && template.sections) {
                        Object.values(template.sections).forEach(section => {
                            if (section.skills) {
                                totalSkillsInTemplate += Object.keys(section.skills).length;
                            }
                        });
                    }

                    const progressPercentage = totalSkillsInTemplate > 0 ?
                        Math.round((progress.completedSkills.length / totalSkillsInTemplate) * 100) : 0;

                    html += `
                        <div class="progress-card ${isCurrent ? 'current' : ''}">
                            <div class="progress-header">
                                <h4>${templateName} ${isCurrent ? '(Current)' : ''}</h4>
                                <div class="progress-stats">
                                    <span class="skills-count">${progress.completedSkills.length}/${totalSkillsInTemplate} skills (${progressPercentage}%)</span>
                                    <span class="sessions-count">${progress.feedbackCount} sessions</span>
                                </div>
                            </div>
                            <div class="progress-date">
                                Last progress: ${new Date(progress.lastUpdated).toLocaleDateString()}
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;

                return html;
            } catch (error) {
                console.error('Error loading historical progress:', error);
                return '';
            }
        }

        // Generate learning timeline
        async function generateLearningTimeline(memberFeedbacks, currentTemplateId) {
            let html = `
                <div class="learning-timeline">
                    <h4>🎯 Session-by-Session Progress</h4>
                    <div class="timeline-container">
            `;

            for (let i = 0; i < memberFeedbacks.length; i++) {
                const feedback = memberFeedbacks[i];
                const activity = activities[feedback.activityId];
                const activityName = activity ? activity.name.en : 'Unknown Activity';
                const date = new Date(feedback.createdAt).toLocaleDateString();
                const isCurrent = i === memberFeedbacks.length - 1;

                // Get skills learned in this session
                const skillsInSession = feedback.skillAssessment?.completedSkills || [];

                // Get skills learned in previous sessions
                const previousSkills = new Set();
                for (let j = 0; j < i; j++) {
                    const prevFeedback = memberFeedbacks[j];
                    if (prevFeedback.skillAssessment?.completedSkills) {
                        prevFeedback.skillAssessment.completedSkills.forEach(skill => previousSkills.add(skill));
                    }
                }

                // Find new skills learned in this session
                const newSkills = skillsInSession.filter(skill => !previousSkills.has(skill));

                // Get skill names from template
                const skillNames = await getSkillNamesFromTemplate(newSkills, feedback.activityId);

                html += `
                    <div class="timeline-item ${isCurrent ? 'current' : ''}">
                        <div class="timeline-marker">
                            <span class="timeline-number">${i + 1}</span>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h5>${activityName} ${isCurrent ? '(Current)' : ''}</h5>
                                <span class="timeline-date">${date}</span>
                            </div>
                            <div class="timeline-stats">
                                <span class="new-skills-count">🆕 ${newSkills.length} new skills</span>
                                <span class="total-skills-count">📊 ${skillsInSession.length} total skills</span>
                            </div>
                            ${newSkills.length > 0 ? `
                                <div class="timeline-skills">
                                    <strong>New skills mastered:</strong>
                                    <ul class="skills-list">
                                        ${skillNames.map(name => `<li>${name}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            html += `
                    </div>
                </div>
            `;

            return html;
        }

        // Get skill names from template
        async function getSkillNamesFromTemplate(skillIds, activityId) {
            const skillNames = [];

            // Get template for this activity
            let templateData = null;
            if (activityId && activities[activityId]) {
                const activity = activities[activityId];
                const templateId = activity.feedbackTemplateId;
                if (templateId && feedbackTemplates[templateId]) {
                    templateData = feedbackTemplates[templateId];
                }
            }

            if (templateData && templateData.sections) {
                // Search through template sections for skill names
                Object.values(templateData.sections).forEach(section => {
                    if (section.skills) {
                        Object.entries(section.skills).forEach(([skillId, skill]) => {
                            if (skillIds.includes(skillId)) {
                                const skillName = skill.name.zh || skill.name.en || skillId;
                                const skillNameEn = skill.name.en || skillId;
                                const displayName = skillName.includes('(') ? skillName : `${skillName} (${skillNameEn})`;
                                skillNames.push(displayName);
                            }
                        });
                    }
                });
            } else {
                // Fallback to default skill names
                const defaultSkills = {
                    'equipment-intro': '滑雪装备介绍 (Equipment Introduction)',
                    'single-foot-familiarity': '单脚熟悉雪板 (Single Foot Board Familiarity)',
                    'single-foot-sliding': '单脚滑板式滑动 (Single Foot Skateboard Sliding)',
                    'single-foot-climbing': '单脚爬坡 (Single Foot Climbing)',
                    'single-foot-straight': '单脚直滑降 (Single Foot Straight Descent)',
                    'single-foot-heel-brake': '单脚脚后跟减速 (Single Foot Heel Braking)',
                    'single-foot-j-turn': '单脚J弯 (Single Foot J-Turn)',
                    'static-gas-pedal': '静态踩油门练习 (Static Gas Pedal Practice)',
                    'single-heel-side-push': '单脚后刃推坡 (Single Foot Heel Side Push)',
                    'single-toe-side-push': '单脚前刃推坡 (Single Foot Toe Side Push)',
                    'both-heel-side-push': '双脚后刃推坡 (Both Feet Heel Side Push)',
                    'both-toe-side-push': '双脚前刃推坡 (Both Feet Toe Side Push)',
                    'both-heel-falling-leaf': '双脚后刃落叶飘 (Both Feet Heel Side Falling Leaf)',
                    'both-toe-falling-leaf': '双脚前刃落叶飘 (Both Feet Toe Side Falling Leaf)',
                    'both-heel-power-falling-leaf': '双脚后刃强力落叶飘 (Both Feet Heel Side Power Falling Leaf)',
                    'both-toe-power-falling-leaf': '双脚前刃强力落叶飘 (Both Feet Toe Side Power Falling Leaf)',
                    'static-rotation': '静态旋转练习 (Static Rotation Practice)',
                    'step-turns': '阶梯转弯 (Step Turns)',
                    'j-turns': 'J弯 (J-Turns)',
                    'walking-edge-change': '走步模拟换刃 (Walking Edge Change Simulation)',
                    'beginner-turns': '新手转弯 (Beginner Turns)',
                    'edge-change-traverse': '换刃后增加横穿雪道 (Edge Change with Slope Traverse)',
                    'traverse-body-movement': '横穿雪道加入身体起伏 (Traverse with Body Movement)',
                    'continuous-edge-change': '连续换刃 (Continuous Edge Changes)',
                    'scrub-360': '搓雪360 (Scrub 360)'
                };

                skillIds.forEach(skillId => {
                    skillNames.push(defaultSkills[skillId] || skillId);
                });
            }

            return skillNames;
        }

        // Load activities and templates on page load
        window.onload = async function() {
            await loadActivities();
            await loadFeedbackTemplates();
        };
    </script>
</body>
</html>
